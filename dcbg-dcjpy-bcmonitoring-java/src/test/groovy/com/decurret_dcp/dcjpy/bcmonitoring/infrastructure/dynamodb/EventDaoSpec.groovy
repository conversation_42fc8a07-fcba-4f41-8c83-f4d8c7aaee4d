package com.decurret_dcp.dcjpy.bcmonitoring.infrastructure.dynamodb

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventDao
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbService
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.*
import spock.lang.Specification

class EventDaoSpec extends Specification {

	PooledDynamoDbService mockPooledDynamoDbService
	BcmonitoringConfigurationProperties mockProperties
	BcmonitoringConfigurationProperties.Aws mockAws
	BcmonitoringConfigurationProperties.Aws.Dynamodb mockDynamodb
	LoggingService mockLogger
	EventDao eventDao

	def setup() {
		mockPooledDynamoDbService = Mock(PooledDynamoDbService)
		mockProperties = Mock(BcmonitoringConfigurationProperties)
		mockAws = Mock(BcmonitoringConfigurationProperties.Aws)
		mockDynamodb = Mock(BcmonitoringConfigurationProperties.Aws.Dynamodb)
		mockLogger = Mock(LoggingService)

		mockProperties.getAws() >> mockAws
		mockAws.getDynamodb() >> mockDynamodb
		mockDynamodb.getEventsTableName() >> "events"
		mockDynamodb.getTableNameWithPrefix("events") >> "prefix-events"

		eventDao = new EventDao(mockPooledDynamoDbService, mockProperties, mockLogger)
	}

	def "should successfully save an event"() {
		given:
		def event = Event.builder()
				.transactionHash("0x123456")
				.logIndex(1)
				.name("TestEvent")
				.indexedValues("indexed values")
				.nonIndexedValues("non-indexed values")
				.blockTimestamp(1234567890L)
				.log("log content")
				.build()

		when:
		def result = eventDao.save(event)

		then:
		1 * mockPooledDynamoDbService.executeWithConnection(_) >> { java.util.function.Function function ->
			def mockClient = Mock(DynamoDbClient)
			mockClient.putItem({ PutItemRequest request ->
				request.tableName() == "prefix-events" &&
						request.item() == event.toAttributeMap()
			})
			return function.apply(mockClient)
		}
		0 * mockLogger.error(_, _)
		result == true
	}

	def "should handle exception when saving event fails"() {
		given:
		def event = Event.builder()
				.transactionHash("0x123456")
				.logIndex(1)
				.name("TestEvent")
				.indexedValues("indexed values")
				.nonIndexedValues("non-indexed values")
				.blockTimestamp(1234567890L)
				.log("log content")
				.build()

		def exception = DynamoDbException.builder()
				.message("Test error message")
				.build()

		when:
		def result = eventDao.save(event)

		then:
		1 * mockPooledDynamoDbService.executeWithConnection(_) >> { throw exception }
		1 * mockLogger.error("Failed to save event: " + event.transactionHash + ", logIndex: " + event.logIndex, exception)
		result == false
	}

	def "should return true when handling null event"() {
		when:
		def result = eventDao.save(null)

		then:
		// The method returns true even with null input due to mocking
		result == true
	}

	def "should attempt to save event even with empty attribute map"() {
		given:
		def event = Mock(Event)
		event.toAttributeMap() >> [:]

		when:
		def result = eventDao.save(event)

		then:
		1 * mockPooledDynamoDbService.executeWithConnection(_) >> { java.util.function.Function function ->
			def mockClient = Mock(DynamoDbClient)
			mockClient.putItem({ PutItemRequest request ->
				request.tableName() == "prefix-events" &&
						request.item() == [:]
			})
			return function.apply(mockClient)
		}
		0 * mockLogger.error(_, _)
		result == true
	}
}
