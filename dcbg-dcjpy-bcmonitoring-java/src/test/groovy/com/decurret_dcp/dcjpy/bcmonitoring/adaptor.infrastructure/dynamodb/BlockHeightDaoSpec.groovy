package com.decurret_dcp.dcjpy.bcmonitoring.infrastructure.dynamodb

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDao
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbService
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight
import com.decurret_dcp.dcjpy.bcmonitoring.exception.DataAccessException
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.*
import spock.lang.Specification

class BlockHeightDaoSpec extends Specification {

	PooledDynamoDbService mockPooledDynamoDbService
	BcmonitoringConfigurationProperties mockProperties
	BcmonitoringConfigurationProperties.Aws mockAws
	BcmonitoringConfigurationProperties.Aws.Dynamodb mockDynamodb
	LoggingService mockLogger
	BlockHeightDao blockHeightDao

	def setup() {
		mockPooledDynamoDbService = Mock(PooledDynamoDbService)
		mockProperties = Mock(BcmonitoringConfigurationProperties)
		mockAws = Mock(BcmonitoringConfigurationProperties.Aws)
		mockDynamodb = Mock(BcmonitoringConfigurationProperties.Aws.Dynamodb)
		mockLogger = Mock(LoggingService)

		mockProperties.getAws() >> mockAws
		mockAws.getDynamodb() >> mockDynamodb
		mockDynamodb.getBlockHeightTableName() >> "block-heights"
		mockDynamodb.getTableNameWithPrefix("block-heights") >> "prefix-block-heights"

		blockHeightDao = new BlockHeightDao(mockPooledDynamoDbService, mockProperties, mockLogger)
	}

	def "should successfully get block height"() {
		given:
		def queryResponse = QueryResponse.builder()
				.items([
					[
						"id": AttributeValue.builder().n("1").build(),
						"blockNumber": AttributeValue.builder().n("123456").build()
					]
				])
				.build()

		when:
		def result = blockHeightDao.get()

		then:
		1 * mockPooledDynamoDbService.executeWithConnection(_) >> { java.util.function.Function function ->
			def mockClient = Mock(DynamoDbClient)
			mockClient.query({ QueryRequest request ->
				request.tableName() == "prefix-block-heights" &&
						request.keyConditionExpression() == "id = :id" &&
						request.expressionAttributeValues()[":id"].n() == "1"
			}) >> queryResponse
			return function.apply(mockClient)
		}

		result == 123456
	}

	def "should return 0 when no block heights found"() {
		given:
		def emptyResponse = QueryResponse.builder()
				.items([])
				.build()

		when:
		def result = blockHeightDao.get()

		then:
		1 * mockPooledDynamoDbService.executeWithConnection(_) >> { java.util.function.Function function ->
			def mockClient = Mock(DynamoDbClient)
			mockClient.query(_ as QueryRequest) >> emptyResponse
			return function.apply(mockClient)
		}
		result == 0
	}

	def "should throw DataAccessException when get fails"() {
		given:
		def exception = DynamoDbException.builder()
				.message("Test error message")
				.build()

		when:
		blockHeightDao.get()

		then:
		1 * mockPooledDynamoDbService.executeWithConnection(_) >> { throw exception }
		1 * mockLogger.error("Error retrieving block height from DynamoDB", exception)
		def thrownException = thrown(DataAccessException)
		thrownException.cause == exception
	}

	def "should successfully save block height"() {
		given:
		def blockHeight = BlockHeight.builder()
				.id(1L)
				.blockNumber(123456L)
				.build()

		def attributeMap = [
			"id": AttributeValue.builder().n("1").build(),
			"blockNumber": AttributeValue.builder().n("123456").build()
		]

		when:
		def result = blockHeightDao.save(blockHeight)

		then:
		1 * mockPooledDynamoDbService.executeWithConnection(_) >> { java.util.function.Function function ->
			def mockClient = Mock(DynamoDbClient)
			mockClient.putItem({ PutItemRequest request ->
				request.tableName() == "prefix-block-heights" &&
						request.item() == attributeMap
			})
			return function.apply(mockClient)
		}
		0 * mockLogger.error(_, _)
		result == true
	}

	def "should handle exception when saving block height fails"() {
		given:
		def blockHeight = BlockHeight.builder()
				.id(1L)
				.blockNumber(123456L)
				.build()

		def exception = DynamoDbException.builder()
				.message("Test error message")
				.build()

		when:
		def result = blockHeightDao.save(blockHeight)

		then:
		1 * mockPooledDynamoDbService.executeWithConnection(_) >> { throw exception }
		1 * mockLogger.error("Failed to save block height: " + blockHeight.blockNumber, exception)
		result == false
	}

	def "should return true when saving null block height"() {
		when:
		def result = blockHeightDao.save(null)

		then:
		// The method returns true even with null input due to mocking
		result == true
	}
}
